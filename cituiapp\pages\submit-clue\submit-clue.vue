<template>
	<view class="page-container">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<u-icon name="arrow-left" size="20" color="#333"></u-icon>
			</view>
			<view class="nav-title">{{ isEditMode ? '编辑放水线索' : '提交放水线索' }}</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 表单内容 -->
		<scroll-view class="content-container" scroll-y>
			<view class="form-container">
				<!-- 选择APP -->
				<view class="form-item">
					<view class="form-label">选择APP</view>
					<SearchSelect
						v-if="!isEditMode"
						v-model="formData.selectedAppId"
						:options="appSelectOptions"
						placeholder="请选择APP"
						title="选择APP"
						@change="onAppChange"
					/>
					<!-- 编辑模式下显示只读的APP信息 -->
					<view v-else class="readonly-app-info">
						<view class="app-name">{{ formData.selectedAppName }}</view>
						<text class="readonly-tip">编辑模式下不可更改APP</text>
					</view>
				</view>
				
				<!-- 放水时间 -->
				<view class="form-item">
					<view class="form-label">放水时间</view>
					<u-datetime-picker 
						v-model="formData.releaseTime"
						mode="datetime"
						:show="showTimePicker"
						@confirm="confirmTime"
						@cancel="showTimePicker = false"
					></u-datetime-picker>
					<view class="select-container" @click="showTimePicker = true">
						<view class="select-input" :class="{ 'placeholder': !formData.releaseTimeText }">
							{{ formData.releaseTimeText || '年/月/日 --:--' }}
						</view>
						<u-icon name="calendar" size="16" color="#999"></u-icon>
					</view>
					<text class="form-tip">默认为当前时间</text>
				</view>

				<!-- 放水金额 -->
				<view class="form-item">
					<view class="form-label">放水金额(元)</view>
					<u-input
						v-model="formData.clueMoney"
						placeholder="如 0.9"
						border="surround"
						:clearable="true"
						type="number"
					></u-input>
				</view>
				
				<!-- 单包大小 -->
				<view class="form-item">
					<view class="form-label">单包大小(元)</view>
					<view class="radio-row">
						<view 
							v-for="(amount, index) in packageAmounts" 
							:key="index"
							class="radio-item"
							:class="{ 'active': formData.packageAmount === amount }"
							@click="selectPackageAmount(amount)"
						>
							<text>{{ amount }}</text>
						</view>
					</view>
				</view>
				
				<!-- 设备型号 -->
				<view class="form-item">
					<view class="form-label">设备型号</view>
					<u-input 
						v-model="formData.deviceModel" 
						placeholder="如：iPhone 15 Pro / 华为mate40"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- APP提现记录截图 -->
				<view class="form-item">
					<view class="form-label">APP提现记录截图</view>
					<ImageUploader
						v-model="formData.picTixian"
						placeholder="点击上传APP提现记录截图"
						tip="支持JPG、PNG格式"
						@change="onAppScreenshotChange"
					/>
				</view>

				<!-- 微信到账记录截图 -->
				<view class="form-item">
					<view class="form-label">微信到账记录截图</view>
					<ImageUploader
						v-model="formData.picDaozhang"
						placeholder="点击上传微信到账记录截图"
						tip="支持JPG、PNG格式"
						@change="onWechatScreenshotChange"
					/>
				</view>

				<!-- 发布人 -->
				<view class="form-item">
					<view class="form-label">发布人</view>
					<u-input
						v-model="formData.nickName"
						placeholder="请输入发布人姓名"
						border="surround"
						:clearable="true"
					></u-input>
				</view>
				
				<!-- 线索描述 -->
				<view class="form-item">
					<view class="form-label">线索描述</view>
					<u-textarea 
						v-model="formData.clueDescription"
						placeholder="详细描述放水情况，包括获得金额、到账时间、注意事项等..."
						:maxlength="300"
						:showWordLimit="true"
						:autoHeight="true"
						border="surround"
					></u-textarea>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<u-button 
						type="primary"
						size="large"
						:loading="isSubmitting"
						:disabled="isSubmitting"
						@click="handleSubmit"
						shape="round"
						customStyle="background: linear-gradient(135deg, #dc2626, #b91c1c); border: none;"
					>
						{{ isSubmitting ? (isEditMode ? '更新中...' : '提交中...') : (isEditMode ? '更新线索' : '提交线索') }}
					</u-button>
					<text class="submit-tip">{{ isEditMode ? '更新后需要重新审核' : '提交并审核通过后将获得30积分奖励' }}</text>
				</view>
			</view>
		</scroll-view>
		

	</view>
</template>

<script>
	import ImageUploader from '@/components/ImageUploader.vue'
	import SearchSelect from '@/components/SearchSelect.vue'
	import { checkAdminPermission } from '@/utils/storage.js'

	export default {
		components: {
			ImageUploader,
			SearchSelect
		},
		data() {
			return {
				showTimePicker: false,
				isSubmitting: false,
				isLoadingApps: false, // APP列表加载状态
				submitTimer: null, // 防抖定时器

				// 编辑模式相关
				isEditMode: false, // 是否为编辑模式
				clueId: null, // 编辑的线索ID
				appList: [], // APP列表数据
				appSelectOptions: [

				], // APP选择器选项

				formData: {
					selectedAppId: '', // APP ID
					selectedAppName: '', // APP名称
					releaseTime: Number(new Date()),
					releaseTimeText: '',
					clueMoney: '', // 放水金额
					packageAmount: '',
					deviceModel: '',
					nickName:'',
					clueDescription: '',
					picTixian: '', // APP提现记录截图
					picDaozhang: '' // 微信到账记录截图
				},

				// 单包金额选项
				packageAmounts: ['0.1-0.29', '0.3-0.49', '0.5-0.99', '1以上']
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},
			
			// APP选择变化
			onAppChange(appId, appName) {
				this.formData.selectedAppId = appId
				this.formData.selectedAppName = appName
			},
			
			// 确认时间选择
			confirmTime(e) {
				this.formData.releaseTime = e.value
				this.formData.releaseTimeText = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
				this.showTimePicker = false
			},
			
			// 选择单包金额
			selectPackageAmount(amount) {
				this.formData.packageAmount = amount
			},
			
			// 图片上传回调方法
			onAppScreenshotChange(imageUrl, imagePath) {
				console.log('APP提现记录截图上传成功:', imageUrl, imagePath)
				this.formData.picTixian = imagePath
			},

			onWechatScreenshotChange(imageUrl, imagePath) {
				console.log('微信到账记录截图上传成功:', imageUrl, imagePath)
				this.formData.picDaozhang = imagePath
			},
			
			// 表单验证
			validateForm() {
				if (!this.formData.selectedAppId) {
					uni.showToast({ title: '请选择APP', icon: 'none' })
					return false
				}

				// 验证放水金额
				if (this.formData.clueMoney !== '' && this.formData.clueMoney !== null) {
					const money = parseFloat(this.formData.clueMoney)
					if (isNaN(money) || money < 0) {
						uni.showToast({ title: '请输入有效的放水金额', icon: 'none' })
						return false
					}
					if (money > 999999.99) {
						uni.showToast({ title: '放水金额不能超过999999.99元', icon: 'none' })
						return false
					}
				}

				if (!this.formData.packageAmount) {
					uni.showToast({ title: '请选择单包大小', icon: 'none' })
					return false
				}

				if (!this.formData.deviceModel || this.formData.deviceModel.trim() === '') {
					uni.showToast({ title: '请输入设备型号', icon: 'none' })
					return false
				}

				if (this.formData.deviceModel.length > 100) {
					uni.showToast({ title: '设备型号不能超过100个字符', icon: 'none' })
					return false
				}

				if (!this.formData.nickName || this.formData.nickName.trim() === '') {
					uni.showToast({ title: '请输入发布人姓名', icon: 'none' })
					return false
				}

				if (!this.formData.clueDescription || this.formData.clueDescription.trim() === '') {
					uni.showToast({ title: '请填写线索描述', icon: 'none' })
					return false
				}

				if (this.formData.clueDescription.length > 300) {
					uni.showToast({ title: '线索描述不能超过300个字符', icon: 'none' })
					return false
				}

				if (!this.formData.picTixian) {
					uni.showToast({ title: '请上传APP提现记录截图', icon: 'none' })
					return false
				}

				if (!this.formData.picDaozhang) {
					uni.showToast({ title: '请上传微信到账记录截图', icon: 'none' })
					return false
				}

				return true
			},
			
			// 提交表单（带防抖）
			handleSubmit() {
				// 防抖处理
				if (this.submitTimer) {
					clearTimeout(this.submitTimer)
				}
				
				this.submitTimer = setTimeout(() => {
					this.submitForm()
				}, 300)
			},
			
			// 加载线索数据（编辑模式）
			async loadClueData() {
				try {
					uni.showLoading({
						title: '加载中...'
					})

					// 调用获取详情接口
					const result = await uni.$u.http.get(`/clue/${this.clueId}`, {
						custom: { auth: true }
					})

					// 填充表单数据
					this.fillFormData(result)

					uni.hideLoading()

				} catch (error) {
					uni.hideLoading()
					console.error('加载线索数据失败:', error)
					uni.showToast({
						title: error.message || '加载失败，请重试',
						icon: 'none'
					})
					// 加载失败时返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				}
			},

			// 填充表单数据
			fillFormData(data) {
				// APP信息（编辑模式下不允许修改APP）
				this.formData.selectedAppId = data.selectedAppId || ''
				this.formData.selectedAppName = data.selectedAppName || ''

				// 放水时间
				if (data.releaseTime) {
					this.formData.releaseTime = data.releaseTime
					this.formData.releaseTimeText = data.releaseTimeText || ''
				}

				// 其他表单字段
				this.formData.clueMoney = data.clueMoney || ''
				this.formData.packageAmount = data.packageAmount || ''
				this.formData.deviceModel = data.deviceModel || ''
				this.formData.nickName = data.nickName || ''
				this.formData.clueDescription = data.clueDescription || ''

				// 图片
				this.formData.picTixian = data.picTixian || ''
				this.formData.picDaozhang = data.picDaozhang || ''
			},

			// 实际提交表单
			async submitForm() {
				if (this.isSubmitting) return

				if (!this.validateForm()) return

				this.isSubmitting = true

				try {
					// 准备提交数据
					const submitData = {
						app_id: parseInt(this.formData.selectedAppId),
						release_time: this.formData.releaseTime, // 已经是毫秒时间戳
						clue_money: this.formData.clueMoney ? parseFloat(this.formData.clueMoney) : 0,
						package_amount: this.formData.packageAmount,
						device_model: this.formData.deviceModel.trim(),
						pic_tixian: this.formData.picTixian || '',
						pic_daozhang: this.formData.picDaozhang || '',
						nick_name: this.formData.nickName.trim(),
						clue_description: this.formData.clueDescription.trim()
					}

					console.log('提交数据:', submitData)

					let result
					if (this.isEditMode) {
						// 编辑模式：调用更新接口
						result = await uni.$u.http.put(`/clue/${this.clueId}`, submitData, {
							custom: { auth: true }
						})
					} else {
						// 新建模式：调用提交接口
						result = await uni.$u.http.post('/clue/submit', submitData, {
							custom: { auth: true }
						})
					}

					console.log('提交结果:', result)

					uni.showToast({
						title: this.isEditMode ? '更新成功' : '提交成功',
						icon: 'success',
						duration: 2000
					})

					// 成功后返回上一页
					setTimeout(() => {
						uni.navigateBack({
							success: () => {
								// 通过事件总线通知列表页面刷新
								uni.$emit('refreshClueList')
							}
						})
					}, 2000)

				} catch (error) {
					console.error('线索提交失败:', error)
					let errorMessage = this.isEditMode ? '更新失败，请重试' : '提交失败，请重试'

					// 处理不同类型的错误
					if (error && error.message) {
						errorMessage = error.message
					} else if (typeof error === 'string') {
						errorMessage = error
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} finally {
					this.isSubmitting = false
				}
			},

			// 加载APP列表
			async loadAppList() {
				if (this.isLoadingApps) return

				this.isLoadingApps = true

				try {
					const result = await uni.$u.http.get('/app/list')
					this.appList = result || []

					// 转换为SearchSelect组件需要的格式
					this.appSelectOptions = this.appList.map(app => ({
						value: app.id,
						text: app.display_text || app.name,
						subtitle: app.category_name
					}))

					if (this.appSelectOptions.length === 0) {
						uni.showToast({
							title: '暂无可选择的APP',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('加载APP列表失败:', error)
					let errorMessage = '加载APP列表失败'

					if (error && error.message) {
						errorMessage = error.message
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})

					// 如果加载失败，提供重试选项
					uni.showModal({
						title: '提示',
						content: '加载APP列表失败，是否重试？',
						success: (res) => {
							if (res.confirm) {
								this.loadAppList()
							}
						}
					})
				} finally {
					this.isLoadingApps = false
				}
			}
		},
		
		onLoad(options) {
			// 检查管理员权限
			if (!checkAdminPermission()) {
				return // 如果没有权限，checkAdminPermission会自动跳转到登录页
			}

			// 检查是否为编辑模式
			if (options.mode === 'edit' && options.clueId) {
				this.isEditMode = true
				this.clueId = parseInt(options.clueId)
			}

			// 初始化时间为当前时间
			this.formData.releaseTimeText = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM')
			// 加载APP列表
			this.loadAppList()

			// 如果是编辑模式，加载线索数据
			if (this.isEditMode) {
				this.loadClueData()
			}
		},
		
		onUnload() {
			// 清理定时器
			if (this.submitTimer) {
				clearTimeout(this.submitTimer)
			}
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 88rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
	
	.nav-left, .nav-right {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-title {
		flex: 1;
		text-align: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
}

.content-container {
	padding-top: 88rpx;
	height: calc(100vh - 88rpx);
}

.form-container {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 40rpx;
	
	.form-label {
		font-size: 28rpx;
		font-weight: 500;
		color: #374151;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 8rpx;
		display: block;
	}
}

.select-container {
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	padding: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.2s ease;
	
	.select-input {
		flex: 1;
		font-size: 28rpx;
		color: #111827;
		
		&.placeholder {
			color: #9ca3af;
		}
	}
}

.select-container:active {
	background-color: #f9fafb;
	border-color: #9ca3af;
}



.radio-row {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.radio-item {
	flex: 1;
	min-width: 160rpx;
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	padding: 24rpx 16rpx;
	text-align: center;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	text {
		font-size: 28rpx;
		color: #374151;
	}
	
	&.active {
		border-color: #dc2626;
		background-color: #fef2f2;
		
		text {
			color: #dc2626;
			font-weight: 500;
		}
	}
	
	&:active {
		transform: scale(0.98);
	}
}

.submit-section {
	margin-top: 60rpx;
	margin-bottom: 60rpx;
	
	.submit-tip {
		display: block;
		text-align: center;
		font-size: 24rpx;
		color: #6b7280;
		margin-top: 16rpx;
	}
}

/* uview组件样式覆盖 */
:deep(.u-input__content) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-textarea) {
	background-color: #ffffff !important;
	border-radius: 16rpx !important;
	border: 2rpx solid #d1d5db !important;
	padding: 24rpx !important;
}

:deep(.u-button) {
	height: 96rpx !important;
	font-size: 32rpx !important;
	font-weight: 600 !important;
}

/* 只读APP信息样式 */
.readonly-app-info {
	padding: 24rpx;
	background-color: #f9fafb;
	border: 1rpx solid #e5e7eb;
	border-radius: 12rpx;

	.app-name {
		font-size: 28rpx;
		font-weight: 500;
		color: #374151;
		margin-bottom: 8rpx;
	}

	.readonly-tip {
		font-size: 24rpx;
		color: #9ca3af;
	}
}
</style> 