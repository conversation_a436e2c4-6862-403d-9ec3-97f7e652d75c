[2025-08-18 10:13:46] local.ERROR: 评测报告提交失败: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'citui.zc_zc_apps' doesn't exist (SQL: select * from `zc_zc_apps` where `app_package` = com.app.www limit 1) {"user_id":3,"data":{"app_name":"测试1","download_url":"https://www.abc.com/d?=12345","app_package":"com.app.www","app_version":"1.2.3","pingfen":3,"category_id":6,"dingbaojine":null,"yunxingmoshi":2,"ceshitiaoshu":1,"xinrenfuli":null,"tixianmenkan":null,"ceshishouyi":null,"ceshishichang":null,"ceshishebei":null,"cepingren":null,"cepingriqi":**********006,"shouyi_1":null,"shouyi_2":null,"shouyi_3":null,"shouyi_4":null,"shouyi_5":null,"pic_main":null,"pic_tixian":null,"pic_daozhang":null,"report_content":null,"logo_url":"http://citui.test.com/upload/image/20250818/175548307327846CCl.png"},"trace":"#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(719): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2635): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2624): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3160): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2623): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(699): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(683): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php(40): App\\Service\\Report\\EvaluationReportService->createOrUpdateApp(Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(28): App\\Service\\Report\\EvaluationReportService->submitReport()
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->submit()
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submit', Array)
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'submit')
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 {main}"} 
[2025-08-18 10:13:46] local.ERROR: 提交失败，请重试 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 提交失败，请重试 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:63)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(28): App\\Service\\Report\\EvaluationReportService->submitReport()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->submit()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submit', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'submit')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-08-18 10:14:57] local.ERROR: date(): Argument #2 ($timestamp) must be of type ?int, float given {"exception":"[object] (TypeError(code: 0): date(): Argument #2 ($timestamp) must be of type ?int, float given at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:164)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php(164): date('Y-m-d', **********.006)
#1 D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php(43): App\\Service\\Report\\EvaluationReportService->prepareReportData(Array, 11, 3)
#2 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(28): App\\Service\\Report\\EvaluationReportService->submitReport()
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->submit()
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submit', Array)
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'submit')
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 {main}
"} 
[2025-08-18 10:54:05] local.ERROR: 测评报告内容不能为空 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 测评报告内容不能为空 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:85)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php(32): App\\Service\\Report\\EvaluationReportService->validateFormData(Array)
#1 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(28): App\\Service\\Report\\EvaluationReportService->submitReport()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->submit()
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submit', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'submit')
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-08-18 14:03:43] local.ERROR: Target class [App\Http\Controllers\Api\V1\Wap\App\AppController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController] does not exist. at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#11 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController\" does not exist at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:875)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(875): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#12 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-08-18 14:03:43] local.ERROR: Target class [App\Http\Controllers\Api\V1\Wap\App\AppController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController] does not exist. at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#11 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController\" does not exist at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:875)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(875): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#12 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-08-18 14:03:43] local.ERROR: Uncaught Whoops\Exception\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\mywork\cituiproject\cituilaravel\vendor\spatie\ignition\src\Solutions\SolutionProviders\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): Whoops\Run->handleError(8192, 'Using ${var} in...', 'D:\\mywork\\citui...', 51)
#1 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): include()
#2 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\mywork\\citui...')
#3 [internal function]: Composer\Autoload\ClassLoader->loadClass('Spatie\\Ignition...')
#4 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(53): class_implements('Spatie\\Ignition...')
#5 [internal function]: Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->Spatie\LaravelIgnition\Solutions\SolutionProviders\{closure}('Spatie\\Ignition...', 1)
#6 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Collection.php(368): Illuminate\Support\Arr::where(Array, Object(Closure))
#8 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(52): Illuminate\Support\Collection->filter(Object(Closure))
#9 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Adapters\Laravel\IgnitionSolutionsRepository.php(36): Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\Exception\ErrorException))
#10 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(244): NunoMaduro\Collision\Adapters\Laravel\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\Exception\ErrorException))
#11 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(123): NunoMaduro\Collision\Writer->renderSolution(Object(Whoops\Exception\Inspector))
#12 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Handler.php(39): NunoMaduro\Collision\Writer->write(Object(Whoops\Exception\Inspector))
#13 D:\mywork\cituiproject\cituilaravel\vendor\filp\whoops\src\Whoops\Run.php(370): NunoMaduro\Collision\Handler->handle(Object(Whoops\Exception\ErrorException))
#14 [internal function]: Whoops\Run->handleException(Object(Whoops\Exception\ErrorException))
#15 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Whoops\\Exception\\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): Whoops\\Run->handleError(8192, 'Using ${var} in...', 'D:\\\\mywork\\\\citui...', 51)
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): include()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\mywork\\\\citui...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Spatie\\\\Ignition...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(53): class_implements('Spatie\\\\Ignition...')
#5 [internal function]: Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\{closure}('Spatie\\\\Ignition...', 1)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(368): Illuminate\\Support\\Arr::where(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(52): Illuminate\\Support\\Collection->filter(Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\IgnitionSolutionsRepository.php(36): Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\\Exception\\ErrorException))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(244): NunoMaduro\\Collision\\Adapters\\Laravel\\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\\Exception\\ErrorException))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(123): NunoMaduro\\Collision\\Writer->renderSolution(Object(Whoops\\Exception\\Inspector))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Handler.php(39): NunoMaduro\\Collision\\Writer->write(Object(Whoops\\Exception\\Inspector))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\filp\\whoops\\src\\Whoops\\Run.php(370): NunoMaduro\\Collision\\Handler->handle(Object(Whoops\\Exception\\ErrorException))
#14 [internal function]: Whoops\\Run->handleException(Object(Whoops\\Exception\\ErrorException))
#15 {main}
  thrown at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:03:43] local.ERROR: Target class [App\Http\Controllers\Api\V1\Wap\App\AppController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController] does not exist. at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#11 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController\" does not exist at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:875)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(875): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(756): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(860): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(692): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(845): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(273): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1085): Illuminate\\Routing\\Route->getController()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1028): Illuminate\\Routing\\Route->controllerMiddleware()
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Route->gatherMiddleware()
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(210): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(155): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(125): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#12 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(560): array_map(Object(Closure), Array, Array)
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(719): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(124): Illuminate\\Support\\Collection->map(Object(Closure))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(110): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(144): Illuminate\\Container\\Container->call(Array)
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(125): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(1002): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(299): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(151): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 {main}
"} 
[2025-08-18 14:03:43] local.ERROR: Uncaught Whoops\Exception\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\mywork\cituiproject\cituilaravel\vendor\spatie\ignition\src\Solutions\SolutionProviders\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): Whoops\Run->handleError(8192, 'Using ${var} in...', 'D:\\mywork\\citui...', 51)
#1 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): include()
#2 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\mywork\\citui...')
#3 [internal function]: Composer\Autoload\ClassLoader->loadClass('Spatie\\Ignition...')
#4 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(53): class_implements('Spatie\\Ignition...')
#5 [internal function]: Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->Spatie\LaravelIgnition\Solutions\SolutionProviders\{closure}('Spatie\\Ignition...', 1)
#6 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Collection.php(368): Illuminate\Support\Arr::where(Array, Object(Closure))
#8 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(52): Illuminate\Support\Collection->filter(Object(Closure))
#9 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Adapters\Laravel\IgnitionSolutionsRepository.php(36): Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\Exception\ErrorException))
#10 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(244): NunoMaduro\Collision\Adapters\Laravel\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\Exception\ErrorException))
#11 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(123): NunoMaduro\Collision\Writer->renderSolution(Object(Whoops\Exception\Inspector))
#12 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Handler.php(39): NunoMaduro\Collision\Writer->write(Object(Whoops\Exception\Inspector))
#13 D:\mywork\cituiproject\cituilaravel\vendor\filp\whoops\src\Whoops\Run.php(370): NunoMaduro\Collision\Handler->handle(Object(Whoops\Exception\ErrorException))
#14 [internal function]: Whoops\Run->handleException(Object(Whoops\Exception\ErrorException))
#15 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Whoops\\Exception\\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): Whoops\\Run->handleError(8192, 'Using ${var} in...', 'D:\\\\mywork\\\\citui...', 51)
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): include()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\mywork\\\\citui...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Spatie\\\\Ignition...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(53): class_implements('Spatie\\\\Ignition...')
#5 [internal function]: Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\{closure}('Spatie\\\\Ignition...', 1)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(368): Illuminate\\Support\\Arr::where(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(52): Illuminate\\Support\\Collection->filter(Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\IgnitionSolutionsRepository.php(36): Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\\Exception\\ErrorException))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(244): NunoMaduro\\Collision\\Adapters\\Laravel\\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\\Exception\\ErrorException))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(123): NunoMaduro\\Collision\\Writer->renderSolution(Object(Whoops\\Exception\\Inspector))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Handler.php(39): NunoMaduro\\Collision\\Writer->write(Object(Whoops\\Exception\\Inspector))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\filp\\whoops\\src\\Whoops\\Run.php(370): NunoMaduro\\Collision\\Handler->handle(Object(Whoops\\Exception\\ErrorException))
#14 [internal function]: Whoops\\Run->handleException(Object(Whoops\\Exception\\ErrorException))
#15 {main}
  thrown at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:14:33] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:14:33] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:14:40] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:14:40] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:14:54] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:15:18] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:15:34] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:15:52] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:15:53] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:16:43] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:16:44] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:15] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:18] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:21] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:27] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:36] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:20:37] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:21:08] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:21:09] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:21:09] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:21:09] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:23:16] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:23:24] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:24:03] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:24:04] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:24:05] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:27:07] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:27:15] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:27:26] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 14:27:30] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 16:44:16] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-18 17:11:48] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:49] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:56] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:56] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:57] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:57] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-18 17:11:58] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 09:49:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'citui.zc_zc_apps' doesn't exist (SQL: select count(*) as aggregate from `zc_zc_apps` where `id` = 14) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'citui.zc_zc_apps' doesn't exist (SQL: select count(*) as aggregate from `zc_zc_apps` where `id` = 14) at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:759)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(719): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2635): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2624): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3160): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2623): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3087): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3015): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(817): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('zc_apps', 'id', 14, NULL, NULL, Array)
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(788): Illuminate\\Validation\\Validator->getExistCount(NULL, 'zc_apps', 'id', 14, Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(611): Illuminate\\Validation\\Validator->validateExists('app_id', 14, Array, Object(Illuminate\\Validation\\Validator))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(417): Illuminate\\Validation\\Validator->validateAttribute('app_id', 'Exists')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(448): Illuminate\\Validation\\Validator->passes()
#14 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController.php(78): Illuminate\\Validation\\Validator->fails()
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController->submitClue(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submitClue', Array)
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController), 'submitClue')
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'citui.zc_zc_apps' doesn't exist at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:413)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(413): PDO->prepare('select count(*)...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(752): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(719): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2635): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2624): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3160): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2623): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3087): Illuminate\\Database\\Query\\Builder->get(Array)
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3015): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(817): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('zc_apps', 'id', 14, NULL, NULL, Array)
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php(788): Illuminate\\Validation\\Validator->getExistCount(NULL, 'zc_apps', 'id', 14, Array)
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(611): Illuminate\\Validation\\Validator->validateExists('app_id', 14, Array, Object(Illuminate\\Validation\\Validator))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(417): Illuminate\\Validation\\Validator->validateAttribute('app_id', 'Exists')
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(448): Illuminate\\Validation\\Validator->passes()
#16 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController.php(78): Illuminate\\Validation\\Validator->fails()
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController->submitClue(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submitClue', Array)
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController), 'submitClue')
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-08-19 10:11:27] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 10:11:44] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 10:12:45] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 10:19:40] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 10:19:51] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 10:20:00] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 11:55:20] local.ERROR: APP不存在或已下架 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在或已下架 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\App\\AppService.php:221)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController.php(34): App\\Service\\App\\AppService->getAppDetail()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController->getAppDetail()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppDetail', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController), 'getAppDetail')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 11:55:35] local.ERROR: APP不存在 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Clue\\ClueService.php:306)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController.php(117): App\\Service\\Clue\\ClueService->getAppClues()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController->getAppClues()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppClues', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController), 'getAppClues')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 11:55:35] local.ERROR: APP不存在或已下架 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在或已下架 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\App\\AppService.php:221)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController.php(34): App\\Service\\App\\AppService->getAppDetail()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController->getAppDetail()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppDetail', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController), 'getAppDetail')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 11:55:55] local.ERROR: APP不存在或已下架 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在或已下架 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\App\\AppService.php:221)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController.php(34): App\\Service\\App\\AppService->getAppDetail()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController->getAppDetail()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppDetail', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\App\\AppController), 'getAppDetail')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 11:55:55] local.ERROR: APP不存在 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Clue\\ClueService.php:306)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController.php(117): App\\Service\\Clue\\ClueService->getAppClues()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController->getAppClues()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppClues', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController), 'getAppClues')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:09:35] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:10:16] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:11:54] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:11:56] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:12:02] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:12:45] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:12:54] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:13:31] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:15:17] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:18:10] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:19:34] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:22:13] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-08-19 14:38:53] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:242)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(40): App\\Service\\Report\\EvaluationReportService->getEvaluationList()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->getEvaluationList()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getEvaluationLi...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'getEvaluationLi...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:39:08] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:242)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(40): App\\Service\\Report\\EvaluationReportService->getEvaluationList()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->getEvaluationList()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getEvaluationLi...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'getEvaluationLi...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:39:55] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:242)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(40): App\\Service\\Report\\EvaluationReportService->getEvaluationList()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->getEvaluationList()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getEvaluationLi...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'getEvaluationLi...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:43:57] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:242)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(40): App\\Service\\Report\\EvaluationReportService->getEvaluationList()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->getEvaluationList()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getEvaluationLi...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'getEvaluationLi...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:44:04] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Report\\EvaluationReportService.php:242)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController.php(40): App\\Service\\Report\\EvaluationReportService->getEvaluationList()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController->getEvaluationList()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getEvaluationLi...', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Report\\EvaluationReportController), 'getEvaluationLi...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: syntax error, unexpected token ";" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \";\" at D:\\mywork\\cituiproject\\cituilaravel\\routes\\api.php:40)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(430): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\mywork\\\\citui...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(386): Illuminate\\Routing\\Router->loadRoutes('D:\\\\mywork\\\\citui...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(165): Illuminate\\Routing\\Router->group(Array, 'D:\\\\mywork\\\\citui...')
#3 D:\\mywork\\cituiproject\\cituilaravel\\app\\Providers\\RouteServiceProvider.php(34): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\mywork\\\\citui...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(914): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 27)
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(913): array_walk(Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(375): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(149): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: Uncaught Whoops\Exception\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\mywork\cituiproject\cituilaravel\vendor\spatie\ignition\src\Solutions\SolutionProviders\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): Whoops\Run->handleError(8192, 'Using ${var} in...', 'D:\\mywork\\citui...', 51)
#1 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): include()
#2 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\mywork\\citui...')
#3 [internal function]: Composer\Autoload\ClassLoader->loadClass('Spatie\\Ignition...')
#4 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(53): class_implements('Spatie\\Ignition...')
#5 [internal function]: Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->Spatie\LaravelIgnition\Solutions\SolutionProviders\{closure}('Spatie\\Ignition...', 1)
#6 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Collection.php(368): Illuminate\Support\Arr::where(Array, Object(Closure))
#8 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(52): Illuminate\Support\Collection->filter(Object(Closure))
#9 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Adapters\Laravel\IgnitionSolutionsRepository.php(36): Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\Exception\ErrorException))
#10 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(244): NunoMaduro\Collision\Adapters\Laravel\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\Exception\ErrorException))
#11 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(123): NunoMaduro\Collision\Writer->renderSolution(Object(Whoops\Exception\Inspector))
#12 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Handler.php(39): NunoMaduro\Collision\Writer->write(Object(Whoops\Exception\Inspector))
#13 D:\mywork\cituiproject\cituilaravel\vendor\filp\whoops\src\Whoops\Run.php(370): NunoMaduro\Collision\Handler->handle(Object(Whoops\Exception\ErrorException))
#14 [internal function]: Whoops\Run->handleException(Object(Whoops\Exception\ErrorException))
#15 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Whoops\\Exception\\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): Whoops\\Run->handleError(8192, 'Using ${var} in...', 'D:\\\\mywork\\\\citui...', 51)
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): include()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\mywork\\\\citui...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Spatie\\\\Ignition...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(53): class_implements('Spatie\\\\Ignition...')
#5 [internal function]: Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\{closure}('Spatie\\\\Ignition...', 1)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(368): Illuminate\\Support\\Arr::where(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(52): Illuminate\\Support\\Collection->filter(Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\IgnitionSolutionsRepository.php(36): Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\\Exception\\ErrorException))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(244): NunoMaduro\\Collision\\Adapters\\Laravel\\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\\Exception\\ErrorException))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(123): NunoMaduro\\Collision\\Writer->renderSolution(Object(Whoops\\Exception\\Inspector))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Handler.php(39): NunoMaduro\\Collision\\Writer->write(Object(Whoops\\Exception\\Inspector))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\filp\\whoops\\src\\Whoops\\Run.php(370): NunoMaduro\\Collision\\Handler->handle(Object(Whoops\\Exception\\ErrorException))
#14 [internal function]: Whoops\\Run->handleException(Object(Whoops\\Exception\\ErrorException))
#15 {main}
  thrown at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51)
[stacktrace]
#0 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: syntax error, unexpected token ";" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \";\" at D:\\mywork\\cituiproject\\cituilaravel\\routes\\api.php:40)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(430): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\mywork\\\\citui...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(386): Illuminate\\Routing\\Router->loadRoutes('D:\\\\mywork\\\\citui...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(165): Illuminate\\Routing\\Router->group(Array, 'D:\\\\mywork\\\\citui...')
#3 D:\\mywork\\cituiproject\\cituilaravel\\app\\Providers\\RouteServiceProvider.php(34): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\mywork\\\\citui...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(914): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 27)
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(913): array_walk(Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(375): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(149): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: Uncaught Whoops\Exception\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\mywork\cituiproject\cituilaravel\vendor\spatie\ignition\src\Solutions\SolutionProviders\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): Whoops\Run->handleError(8192, 'Using ${var} in...', 'D:\\mywork\\citui...', 51)
#1 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): include()
#2 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\mywork\\citui...')
#3 [internal function]: Composer\Autoload\ClassLoader->loadClass('Spatie\\Ignition...')
#4 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(53): class_implements('Spatie\\Ignition...')
#5 [internal function]: Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->Spatie\LaravelIgnition\Solutions\SolutionProviders\{closure}('Spatie\\Ignition...', 1)
#6 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Collection.php(368): Illuminate\Support\Arr::where(Array, Object(Closure))
#8 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(52): Illuminate\Support\Collection->filter(Object(Closure))
#9 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Adapters\Laravel\IgnitionSolutionsRepository.php(36): Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\Exception\ErrorException))
#10 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(244): NunoMaduro\Collision\Adapters\Laravel\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\Exception\ErrorException))
#11 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(123): NunoMaduro\Collision\Writer->renderSolution(Object(Whoops\Exception\Inspector))
#12 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Handler.php(39): NunoMaduro\Collision\Writer->write(Object(Whoops\Exception\Inspector))
#13 D:\mywork\cituiproject\cituilaravel\vendor\filp\whoops\src\Whoops\Run.php(370): NunoMaduro\Collision\Handler->handle(Object(Whoops\Exception\ErrorException))
#14 [internal function]: Whoops\Run->handleException(Object(Whoops\Exception\ErrorException))
#15 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Whoops\\Exception\\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): Whoops\\Run->handleError(8192, 'Using ${var} in...', 'D:\\\\mywork\\\\citui...', 51)
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): include()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\mywork\\\\citui...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Spatie\\\\Ignition...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(53): class_implements('Spatie\\\\Ignition...')
#5 [internal function]: Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\{closure}('Spatie\\\\Ignition...', 1)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(368): Illuminate\\Support\\Arr::where(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(52): Illuminate\\Support\\Collection->filter(Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\IgnitionSolutionsRepository.php(36): Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\\Exception\\ErrorException))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(244): NunoMaduro\\Collision\\Adapters\\Laravel\\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\\Exception\\ErrorException))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(123): NunoMaduro\\Collision\\Writer->renderSolution(Object(Whoops\\Exception\\Inspector))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Handler.php(39): NunoMaduro\\Collision\\Writer->write(Object(Whoops\\Exception\\Inspector))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\filp\\whoops\\src\\Whoops\\Run.php(370): NunoMaduro\\Collision\\Handler->handle(Object(Whoops\\Exception\\ErrorException))
#14 [internal function]: Whoops\\Run->handleException(Object(Whoops\\Exception\\ErrorException))
#15 {main}
  thrown at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51)
[stacktrace]
#0 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: syntax error, unexpected token ";" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \";\" at D:\\mywork\\cituiproject\\cituilaravel\\routes\\api.php:40)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(430): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\mywork\\\\citui...')
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(386): Illuminate\\Routing\\Router->loadRoutes('D:\\\\mywork\\\\citui...')
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(165): Illuminate\\Routing\\Router->group(Array, 'D:\\\\mywork\\\\citui...')
#3 D:\\mywork\\cituiproject\\cituilaravel\\app\\Providers\\RouteServiceProvider.php(34): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\mywork\\\\citui...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(651): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(914): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 27)
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(913): array_walk(Array, Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(242): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(375): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(149): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 D:\\mywork\\cituiproject\\cituilaravel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-08-19 14:48:08] local.ERROR: Uncaught Whoops\Exception\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\mywork\cituiproject\cituilaravel\vendor\spatie\ignition\src\Solutions\SolutionProviders\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): Whoops\Run->handleError(8192, 'Using ${var} in...', 'D:\\mywork\\citui...', 51)
#1 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(571): include()
#2 D:\mywork\cituiproject\cituilaravel\vendor\composer\ClassLoader.php(428): Composer\Autoload\includeFile('D:\\mywork\\citui...')
#3 [internal function]: Composer\Autoload\ClassLoader->loadClass('Spatie\\Ignition...')
#4 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(53): class_implements('Spatie\\Ignition...')
#5 [internal function]: Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->Spatie\LaravelIgnition\Solutions\SolutionProviders\{closure}('Spatie\\Ignition...', 1)
#6 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\mywork\cituiproject\cituilaravel\vendor\laravel\framework\src\Illuminate\Collections\Collection.php(368): Illuminate\Support\Arr::where(Array, Object(Closure))
#8 D:\mywork\cituiproject\cituilaravel\vendor\spatie\laravel-ignition\src\Solutions\SolutionProviders\SolutionProviderRepository.php(52): Illuminate\Support\Collection->filter(Object(Closure))
#9 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Adapters\Laravel\IgnitionSolutionsRepository.php(36): Spatie\LaravelIgnition\Solutions\SolutionProviders\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\Exception\ErrorException))
#10 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(244): NunoMaduro\Collision\Adapters\Laravel\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\Exception\ErrorException))
#11 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Writer.php(123): NunoMaduro\Collision\Writer->renderSolution(Object(Whoops\Exception\Inspector))
#12 D:\mywork\cituiproject\cituilaravel\vendor\nunomaduro\collision\src\Handler.php(39): NunoMaduro\Collision\Writer->write(Object(Whoops\Exception\Inspector))
#13 D:\mywork\cituiproject\cituilaravel\vendor\filp\whoops\src\Whoops\Run.php(370): NunoMaduro\Collision\Handler->handle(Object(Whoops\Exception\ErrorException))
#14 [internal function]: Whoops\Run->handleException(Object(Whoops\Exception\ErrorException))
#15 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Whoops\\Exception\\ErrorException: Using ${var} in strings is deprecated, use {$var} instead in D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51
Stack trace:
#0 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): Whoops\\Run->handleError(8192, 'Using ${var} in...', 'D:\\\\mywork\\\\citui...', 51)
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(571): include()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\mywork\\\\citui...')
#3 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('Spatie\\\\Ignition...')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(53): class_implements('Spatie\\\\Ignition...')
#5 [internal function]: Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\{closure}('Spatie\\\\Ignition...', 1)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(795): array_filter(Array, Object(Closure), 1)
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(368): Illuminate\\Support\\Arr::where(Array, Object(Closure))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\laravel-ignition\\src\\Solutions\\SolutionProviders\\SolutionProviderRepository.php(52): Illuminate\\Support\\Collection->filter(Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\IgnitionSolutionsRepository.php(36): Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SolutionProviderRepository->getSolutionsForThrowable(Object(Whoops\\Exception\\ErrorException))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(244): NunoMaduro\\Collision\\Adapters\\Laravel\\IgnitionSolutionsRepository->getFromThrowable(Object(Whoops\\Exception\\ErrorException))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Writer.php(123): NunoMaduro\\Collision\\Writer->renderSolution(Object(Whoops\\Exception\\Inspector))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\nunomaduro\\collision\\src\\Handler.php(39): NunoMaduro\\Collision\\Writer->write(Object(Whoops\\Exception\\Inspector))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\filp\\whoops\\src\\Whoops\\Run.php(370): NunoMaduro\\Collision\\Handler->handle(Object(Whoops\\Exception\\ErrorException))
#14 [internal function]: Whoops\\Run->handleException(Object(Whoops\\Exception\\ErrorException))
#15 {main}
  thrown at D:\\mywork\\cituiproject\\cituilaravel\\vendor\\spatie\\ignition\\src\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider.php:51)
[stacktrace]
#0 {main}
"} 
[2025-08-19 14:59:58] local.ERROR: APP不存在 {"exception":"[object] (App\\Exceptions\\MyException(code: 0): APP不存在 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\Clue\\ClueService.php:335)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController.php(117): App\\Service\\Clue\\ClueService->getAppClues()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController->getAppClues()
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getAppClues', Array)
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(258): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\Wap\\Clue\\ClueController), 'getAppClues')
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(204): Illuminate\\Routing\\Route->runController()
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(725): Illuminate\\Routing\\Route->run()
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 {main}
"} 
)
#38 {main}
"} 
[2025-08-19 15:07:09] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:07:11] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:07:12] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:07:13] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:07:13] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:07:13] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:10:20] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:10:20] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 15:10:24] local.ERROR: Declaration of App\Http\Requests\Citui\SubmitEvaluationRequest::validated() must be compatible with Illuminate\Foundation\Http\FormRequest::validated($key = null, $default = null) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Http\\Requests\\Citui\\SubmitEvaluationRequest::validated() must be compatible with Illuminate\\Foundation\\Http\\FormRequest::validated($key = null, $default = null) at D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Requests\\Citui\\SubmitEvaluationRequest.php:335)
[stacktrace]
#0 {main}
"} 
[2025-08-19 16:40:17] local.ERROR: 请先登录 {"exception":"[object] (App\\Exceptions\\MyException(code: 401): 请先登录 at D:\\mywork\\cituiproject\\cituilaravel\\app\\Service\\User\\Auth\\LoginService.php:31)
[stacktrace]
#0 D:\\mywork\\cituiproject\\cituilaravel\\app\\Http\\Middleware\\UserLogin.php(18): App\\Service\\User\\Auth\\LoginService->loginAuth()
#1 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserLogin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(92): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#6 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#7 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(724): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(703): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(667): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(656): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\mywork\\cituiproject\\cituilaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\mywork\\cituiproject\\cituilaravel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
